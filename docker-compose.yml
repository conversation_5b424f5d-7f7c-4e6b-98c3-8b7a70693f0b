version: "3.8"

services:
  vault:
    image: hashicorp/vault:1.16
    container_name: sma-vault
    restart: unless-stopped
    environment:
      VAULT_DEV_ROOT_TOKEN_ID: root
      VAULT_DEV_LISTEN_ADDRESS: "0.0.0.0:8200"
      VAULT_LOG_LEVEL: info
    ports:
      - "8200:8200"
    volumes:
      - vault-data:/vault/file
      - vault-logs:/vault/logs
    cap_add:
      - IPC_LOCK
    networks:
      - sma-network
    healthcheck:
      test: ["CMD", "vault", "status"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  redis:
    image: redis:7-alpine
    container_name: sma-redis
    restart: unless-stopped
    command: [
      "redis-server",
      "--save", "900", "1",
      "--save", "300", "10",
      "--save", "60", "10000",
      "--loglevel", "notice",
      "--maxmemory", "512mb",
      "--maxmemory-policy", "allkeys-lru"
    ]
    volumes:
      - redis-data:/data
    ports:
      - "6379:6379"
    networks:
      - sma-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  prometheus:
    image: prom/prometheus:v2.52.0
    container_name: sma-prometheus
    restart: unless-stopped
    volumes:
      - ./monitoring/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - ./monitoring/prometheus/alerts.yml:/etc/prometheus/alerts.yml
      - prometheus-data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=30d'
      - '--web.enable-lifecycle'
      - '--web.enable-admin-api'
      - '--web.external-url=http://localhost:9090'
    ports:
      - "9090:9090"
    networks:
      - sma-network
    depends_on:
      - bot
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:9090/-/healthy"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  grafana:
    image: grafana/grafana:11.0.0
    container_name: sma-grafana
    restart: unless-stopped
    depends_on:
      - prometheus
    volumes:
      - ./monitoring/grafana/dashboards:/var/lib/grafana/dashboards
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
      - grafana-data:/var/lib/grafana
    environment:
      GF_SECURITY_ADMIN_PASSWORD: admin
      GF_USERS_ALLOW_SIGN_UP: false
      GF_INSTALL_PLUGINS: "grafana-piechart-panel,grafana-clock-panel"
      GF_SERVER_ROOT_URL: "http://localhost:3000"
      GF_ANALYTICS_REPORTING_ENABLED: false
      GF_ANALYTICS_CHECK_FOR_UPDATES: false
    ports:
      - "3000:3000"
    networks:
      - sma-network
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:3000/api/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  bot:
    build: .
    container_name: sma-trend-follower
    restart: unless-stopped
    depends_on:
      redis:
        condition: service_healthy
      vault:
        condition: service_healthy
    environment:
      # Vault configuration
      VAULT_ADDR: "http://vault:8200"
      VAULT_TOKEN: "root"
      
      # Application configuration
      ASPNETCORE_ENVIRONMENT: "Production"
      PROM_INSTANCE: "bot-01"
      
      # Redis configuration
      REDIS_URL: "redis:6379"
      
      # Logging configuration
      SERILOG_MINIMUM_LEVEL: "Information"
      
      # Trading configuration
      TRADING_ENVIRONMENT: "paper"
      
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
      - ./cache:/app/cache
    ports:
      - "5000:5000"
    networks:
      - sma-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 120s

volumes:
  vault-data:
    driver: local
  vault-logs:
    driver: local
  redis-data:
    driver: local
  prometheus-data:
    driver: local
  grafana-data:
    driver: local

networks:
  sma-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
