version: "3.8"

volumes:
  vault-data:
  redis-data:
  prometheus-data:
  grafana-data:
  gitea-data:
  actions-runner-data:

services:
  vault:
    image: hashicorp/vault:1.16
    container_name: vault
    restart: unless-stopped
    environment:
      VAULT_DEV_ROOT_TOKEN_ID: root
      VAULT_DEV_LISTEN_ADDRESS: "0.0.0.0:8200"
    ports: ["8200:8200"]
    volumes: [ vault-data:/vault/file ]

  redis:
    image: redis:7-alpine
    container_name: redis
    restart: unless-stopped
    command: ["redis-server", "--save", "900", "1", "--save", "300", "10"]
    volumes: [ redis-data:/data ]
    ports: ["6379:6379"]

  prometheus:
    image: prom/prometheus:v2.52.0
    container_name: prometheus
    restart: unless-stopped
    volumes:
      - ./monitoring/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - ./monitoring/prometheus/alerts.yml:/etc/prometheus/alerts.yml
      - prometheus-data:/prometheus
    ports: ["9090:9090"]

  grafana:
    image: grafana/grafana:11.0.0
    container_name: grafana
    restart: unless-stopped
    depends_on: [ prometheus ]
    environment:
      GF_INSTALL_PLUGINS: "grafana-piechart-panel"
    volumes:
      - grafana-data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/var/lib/grafana/dashboards
      - ./monitoring/grafana/provisioning/dashboards:/etc/grafana/provisioning/dashboards
    ports: ["3000:3000"]

  gitea:
    image: gitea/gitea:1.22
    container_name: gitea
    restart: unless-stopped
    environment:
      USER_UID: 1000
      USER_GID: 1000
      GITEA__server__DOMAIN: gitea.local
      GITEA__server__ROOT_URL: http://gitea.local:3001/
    volumes: [ gitea-data:/data ]
    ports: ["3001:3000","2222:22"]

  actions-runner:
    image: myoung34/github-runner:latest
    container_name: actions-runner
    restart: unless-stopped
    depends_on: [ gitea ]
    environment:
      RUNNER_NAME: local-runner
      RUNNER_WORKDIR: /runner/_work
      RUNNER_SCOPE: repo
      GITHUB_URL: https://github.com/patco1/SmaTrendFollower
      RUNNER_TOKEN: ${GITHUB_RUNNER_TOKEN:-dummy}
    volumes: [ actions-runner-data:/runner ]

  bot:
    build: .
    container_name: sma-trend-follower
    restart: unless-stopped
    depends_on: [ redis, vault ]
    environment:
      ASPNETCORE_ENVIRONMENT: Production
      VAULT_ADDR:  http://vault:8200
      VAULT_TOKEN: root
      PROM_INSTANCE: bot-01
    ports: ["5000:5000"]
