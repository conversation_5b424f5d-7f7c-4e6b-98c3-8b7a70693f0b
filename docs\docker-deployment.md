# Docker Deployment Guide

This guide covers deploying SmaTrendFollower using Docker and Docker Compose for a complete production-ready stack.

## Overview

The Docker deployment includes:
- **SmaTrendFollower Bot**: The main trading application
- **HashiCorp Vault**: Secure secrets management
- **Redis**: Caching and state management
- **Prometheus**: Metrics collection and monitoring
- **Grafana**: Dashboards and visualization

## Prerequisites

1. **Docker & Docker Compose**: Install Docker Desktop or Docker Engine with Docker Compose
2. **Git**: For cloning and updating the repository
3. **API Keys**: Polygon.io and Alpaca Markets credentials

## Quick Start

### 1. Clone and Setup

```bash
git clone <repository-url>
cd SmaTrendFollower
```

### 2. Deploy the Stack

**Clean-Slate Deployment (Recommended):**
```bash
# Run once after cloning on the server
chmod +x scripts/recreate.sh
./scripts/recreate.sh
```

**Standard Deployment:**

**Linux/macOS:**
```bash
./scripts/deploy.sh
```

**Windows (PowerShell):**
```powershell
.\scripts\deploy.ps1
```

### 3. Configure Secrets

**Linux/macOS:**
```bash
./scripts/setup-vault.sh
```

**Windows (PowerShell):**
```powershell
.\scripts\setup-vault.ps1
```

## Clean-Slate Deployment

The `scripts/recreate.sh` script provides a clean-slate deployment approach that:

1. **Stops old containers** with volume cleanup
2. **Prunes dangling volumes** to ensure fresh state
3. **Builds bot image** from current codebase
4. **Starts new stack** with all services
5. **Tails bot logs** for immediate feedback

This approach is ideal for:
- Initial deployment on a new server
- Complete environment refresh
- Troubleshooting persistent issues
- Development environment reset

**Usage:**
```bash
# Make executable (first time only)
chmod +x scripts/recreate.sh

# Run clean-slate deployment
./scripts/recreate.sh
```

**Note:** This will remove all existing data in Docker volumes. Ensure you have backups if needed.

## Manual Deployment

### 1. Build and Start Services

```bash
# Build the bot image
docker-compose build bot

# Start all services
docker-compose up -d

# Check service status
docker-compose ps
```

### 2. Configure Vault Secrets

```bash
# Set environment variables
export VAULT_ADDR="http://localhost:8200"
export VAULT_TOKEN="root"

# Store secrets
vault kv put secret/sma \
    POLYGON_API_KEY="your-polygon-key" \
    ALPACA_KEY_ID="your-alpaca-key" \
    ALPACA_SECRET="your-alpaca-secret" \
    DISCORD_BOT_TOKEN="your-discord-token" \
    DISCORD_CHANNEL_ID="your-channel-id" \
    OPENAI_API_KEY="your-openai-key"
```

## Service URLs

Once deployed, access the services at:

- **Trading Bot**: http://localhost:5000
  - Health: http://localhost:5000/health
  - Metrics: http://localhost:5000/metrics
- **Vault**: http://localhost:8200 (token: `root`)
- **Grafana**: http://localhost:3000 (admin/admin)
- **Prometheus**: http://localhost:9090
- **Redis**: localhost:6379

## Configuration

### Environment Variables

The bot supports these environment variables:

```yaml
# Vault Configuration
VAULT_ADDR: "http://vault:8200"
VAULT_TOKEN: "root"

# Application Configuration
ASPNETCORE_ENVIRONMENT: "Production"
PROM_INSTANCE: "bot-01"

# Redis Configuration
REDIS_URL: "redis:6379"

# Trading Configuration
TRADING_ENVIRONMENT: "paper"  # or "live"
```

### Volume Mounts

The following directories are mounted for persistence:

- `./logs:/app/logs` - Application logs
- `./data:/app/data` - Application data
- `./cache:/app/cache` - Cache files

## Management Commands

### View Logs

```bash
# All services
docker-compose logs -f

# Specific service
docker-compose logs -f bot
docker-compose logs -f vault
docker-compose logs -f redis
```

### Restart Services

```bash
# Restart all services
docker-compose restart

# Restart specific service
docker-compose restart bot
```

### Update and Redeploy

```bash
# Pull latest code
git pull origin main

# Rebuild and restart
docker-compose build bot
docker-compose up -d bot
```

### Stop Services

```bash
# Stop all services
docker-compose down

# Stop and remove volumes (WARNING: Data loss)
docker-compose down -v
```

## Monitoring

### Health Checks

All services include health checks:

```bash
# Check service health
docker-compose ps

# Manual health check
curl http://localhost:5000/health
```

### Metrics

- **Prometheus**: Collects metrics from all services
- **Grafana**: Pre-configured dashboards for trading metrics
- **Bot Metrics**: Available at `/metrics` endpoint

### Alerts

Prometheus includes alerts for:
- Bot down/unhealthy
- High drawdown conditions
- API connectivity issues
- Memory/CPU usage

## Troubleshooting

### Common Issues

1. **Services not starting**: Check Docker daemon is running
2. **Port conflicts**: Ensure ports 3000, 5000, 6379, 8200, 9090 are available
3. **Vault connection issues**: Verify Vault is healthy before starting bot
4. **Redis connection issues**: Check Redis container logs

### Debug Commands

```bash
# Check container status
docker-compose ps

# View container logs
docker-compose logs [service-name]

# Execute commands in container
docker-compose exec bot bash
docker-compose exec redis redis-cli

# Check network connectivity
docker-compose exec bot ping redis
docker-compose exec bot ping vault
```

### Log Locations

- **Application Logs**: `./logs/` directory
- **Container Logs**: `docker-compose logs`
- **Vault Logs**: Vault container logs
- **Redis Logs**: Redis container logs

## Security Considerations

1. **Vault Token**: Change the default root token in production
2. **Network**: Use Docker networks for service isolation
3. **Secrets**: Never commit secrets to version control
4. **Firewall**: Restrict external access to internal ports
5. **Updates**: Regularly update base images and dependencies

## Production Deployment

For production deployment:

1. **Use external Vault**: Deploy Vault separately with proper authentication
2. **External Redis**: Use managed Redis service for high availability
3. **Load Balancer**: Add load balancer for multiple bot instances
4. **Monitoring**: Set up external monitoring and alerting
5. **Backup**: Implement backup strategy for data and configurations

## Support

For issues and questions:
1. Check the logs: `docker-compose logs -f`
2. Verify service health: `docker-compose ps`
3. Review this documentation
4. Check the main README.md for additional troubleshooting
